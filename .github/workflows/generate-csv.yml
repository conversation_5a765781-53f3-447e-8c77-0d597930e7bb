name: Generate CSV from latest.json

on:
  push:
    paths:
      - 'data/latest.json'
  workflow_dispatch: # Allow manual triggering

jobs:
  generate-csv:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Generate CSV from latest.json
        run: npm run latest-csv

      - name: Commit and push CSV if changes
        run: |
          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'
          git add data/latest.csv
          git diff --quiet && git diff --staged --quiet || (git commit -m "Auto-generate latest.csv from latest.json" && git push)
