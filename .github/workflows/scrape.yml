name: Scrape SG60 CDC Vouchers Locations Data

on:
  schedule:
    # Run at 0AM UTC (8AM SGT)
    - cron: '0 0 * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  scrape:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Scrape and process data
        run: node scrape.js

      - name: Commit and push if changes
        run: |
          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'
          git add -A
          git diff --quiet && git diff --staged --quiet || (git commit -m "Update data $(date +'%Y-%m-%d')" && git push)
