import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DATA_DIR = path.join(__dirname, 'data');
const LATEST_DATA_PATH = path.join(DATA_DIR, 'latest.json');
const RESET_CHANGELOG_PATH = path.join(__dirname, 'reset-changelog.md');

async function generateResetChangelog() {
  try {
    const latestData = await fs.readJson(LATEST_DATA_PATH);
    const locations = latestData.locations;

    // Group locations by year, month, and date
    const groupedByYearMonth = {};
    for (const location of locations) {
      if (!location.lastResetDate) continue;
      const date = new Date(location.lastResetDate);
      const year = date.getFullYear();
      const month = date.toLocaleString('default', { month: 'long' });
      const dateStr = location.lastResetDate;

      if (!groupedByYearMonth[year]) {
        groupedByYearMonth[year] = {};
      }
      if (!groupedByYearMonth[year][month]) {
        groupedByYearMonth[year][month] = {};
      }
      if (!groupedByYearMonth[year][month][dateStr]) {
        groupedByYearMonth[year][month][dateStr] = [];
      }
      groupedByYearMonth[year][month][dateStr].push(location);
    }

    // Sort years, months, and dates in descending order
    const sortedYears = Object.keys(groupedByYearMonth).sort((a, b) => b - a);

    let markdown = '# CDC Vouchers Reset Log\n\n';

    // Generate Table of Contents
    markdown += '## Table of Contents\n\n';
    for (const year of sortedYears) {
      markdown += `- [${year}](#${year})\n`;

      const monthNames = Object.keys(groupedByYearMonth[year]);
      const sortedMonths = monthNames.sort((a, b) => {
        const monthA = new Date(`${a} 1, 2000`).getMonth();
        const monthB = new Date(`${b} 1, 2000`).getMonth();
        return monthB - monthA;
      });

      for (const month of sortedMonths) {
        const monthAnchor = month.toLowerCase();
        markdown += `  - [${month}](#${monthAnchor})\n`;
      }
    }
    markdown += '\n';

    let isFirstDate = true;

    for (const year of sortedYears) {
      markdown += `# ${year}\n\n`;

      const monthNames = Object.keys(groupedByYearMonth[year]);
      const sortedMonths = monthNames.sort((a, b) => {
        const monthA = new Date(`${a} 1, 2000`).getMonth();
        const monthB = new Date(`${b} 1, 2000`).getMonth();
        return monthB - monthA;
      });

      for (const month of sortedMonths) {
        markdown += `## ${month}\n\n`;

        const dates = Object.keys(groupedByYearMonth[year][month]);
        const sortedDates = dates.sort((a, b) => new Date(b) - new Date(a));

        for (const date of sortedDates) {
          const locations = groupedByYearMonth[year][month][date];
          const dayNumber = new Date(date).getDate();
          markdown += `<details${
            isFirstDate ? ' open' : ''
          }><summary><b>${dayNumber}</b> (${locations.length})</summary>\n\n`;
          markdown += '| Name | Address | Coordinates |\n|---|---|---|\n';

          for (const location of locations) {
            const coords =
              location.LAT && location.LON
                ? `<span title="${location.LAT},${
                    location.LON
                  }">${location.LAT.toFixed(5)}, ${location.LON.toFixed(
                    5,
                  )}</span>`
                : 'N/A';
            markdown += `| ${location.name} | ${location.address} | ${coords} |\n`;
          }

          markdown += '\n</details>\n\n';
          isFirstDate = false;
        }
      }
    }

    await fs.writeFile(RESET_CHANGELOG_PATH, markdown);
    console.log('Reset changelog generated successfully');
  } catch (error) {
    console.error('Error generating reset changelog:', error);
    process.exit(1);
  }
}

generateResetChangelog();
